import { Agent } from 'http';
import { Agent as HttpsAgent } from 'https';

/**
 * HTTP configuration utility to override default Node.js/undici timeouts
 * and provide consistent timeout behavior across the application.
 */
export class HttpConfig {
  /**
   * Create HTTP agents with custom timeout configurations
   * to override the default 300000ms (5 minute) timeout from undici
   */
  static createHttpAgents(timeoutMs: number = 180000) { // Default 3 minutes
    const httpAgent = new Agent({
      timeout: timeoutMs,
      keepAlive: true,
      keepAliveMsecs: 30000,
      maxSockets: 50,
      maxFreeSockets: 10
    });

    const httpsAgent = new HttpsAgent({
      timeout: timeoutMs,
      keepAlive: true,
      keepAliveMsecs: 30000,
      maxSockets: 50,
      maxFreeSockets: 10
    });

    return { httpAgent, httpsAgent };
  }

  /**
   * Configure global HTTP defaults to override undici settings
   */
  static configureGlobalDefaults() {
    // Set Node.js HTTP timeout defaults
    if (process.env['NODE_ENV'] !== 'test') {
      // Override undici defaults
      process.env['UNDICI_CONNECT_TIMEOUT'] = '30000'; // 30 seconds
      process.env['UNDICI_HEADERS_TIMEOUT'] = '60000'; // 1 minute
      process.env['UNDICI_BODY_TIMEOUT'] = '180000';   // 3 minutes
    }
  }

  /**
   * Get axios configuration with proper timeout settings
   */
  static getAxiosConfig(timeoutMs: number = 180000) {
    const { httpAgent, httpsAgent } = this.createHttpAgents(timeoutMs);
    
    return {
      timeout: timeoutMs,
      httpAgent,
      httpsAgent,
      // Additional axios-specific timeout configurations
      transitional: {
        clarifyTimeoutError: true
      }
    };
  }
}

// Configure global defaults when this module is imported
HttpConfig.configureGlobalDefaults();
