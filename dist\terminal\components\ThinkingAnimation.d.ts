export declare class ThinkingAnimation {
    private frames;
    private currentFrame;
    private startTime;
    private intervalId;
    private isRunning;
    private lastOutput;
    private isTerminalReady;
    constructor();
    start(message?: string): void;
    stop(): void;
    private updateFrame;
    updateMessage(newMessage: string): void;
    isActive(): boolean;
    getElapsedTime(): number;
    handleResize(): void;
}
export declare class ProgressAnimation {
    private progressChars;
    private currentFrame;
    private intervalId;
    private isRunning;
    private lastOutput;
    private isTerminalReady;
    private lastPercentage;
    constructor();
    start(message: string, percentage?: number): void;
    update(message: string, percentage?: number): void;
    stop(): void;
    private updateFrame;
    private createProgressBar;
    isActive(): boolean;
}
export declare class StatusIndicator {
    static success(message: string): void;
    static error(message: string): void;
    static warning(message: string): void;
    static info(message: string): void;
    static loading(message: string): void;
}
export declare class AnimationManager {
    private static activeAnimations;
    static register(animation: ThinkingAnimation | ProgressAnimation): void;
    static unregister(animation: ThinkingAnimation | ProgressAnimation): void;
    static stopAll(): void;
    static handleProcessExit(): void;
}
//# sourceMappingURL=ThinkingAnimation.d.ts.map