import { Agent } from 'http';
import { Agent as HttpsAgent } from 'https';
/**
 * HTTP configuration utility to override default Node.js/undici timeouts
 * and provide consistent timeout behavior across the application.
 */
export declare class HttpConfig {
    /**
     * Create HTTP agents with custom timeout configurations
     * to override the default 300000ms (5 minute) timeout from undici
     */
    static createHttpAgents(timeoutMs?: number): {
        httpAgent: Agent;
        httpsAgent: HttpsAgent;
    };
    /**
     * Configure global HTTP defaults to override undici settings
     */
    static configureGlobalDefaults(): void;
    /**
     * Get axios configuration with proper timeout settings
     */
    static getAxiosConfig(timeoutMs?: number): {
        timeout: number;
        httpAgent: Agent;
        httpsAgent: HttpsAgent;
        transitional: {
            clarifyTimeoutError: boolean;
        };
    };
}
//# sourceMappingURL=HttpConfig.d.ts.map