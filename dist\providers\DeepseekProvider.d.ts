import { ChatMessage, DeepseekConfig } from '../types/index.js';
export declare class DeepseekProvider {
    private client;
    private config;
    private retryLogic;
    private shellTool;
    constructor(config: DeepseekConfig);
    chat(messages: ChatMessage[], model?: string): Promise<ChatMessage>;
    private formatMessages;
    private getAvailableTools;
    private executeToolCalls;
    getAvailableModels(): Promise<string[]>;
    validateConnection(): Promise<boolean>;
    testConnection(): Promise<{
        success: boolean;
        error?: string;
        latency?: number;
    }>;
    private generateId;
    updateConfig(config: DeepseekConfig): void;
    getConfig(): DeepseekConfig;
}
//# sourceMappingURL=DeepseekProvider.d.ts.map