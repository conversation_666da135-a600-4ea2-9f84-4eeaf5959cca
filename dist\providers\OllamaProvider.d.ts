import { ChatMessage, OllamaConfig } from '../types/index.js';
export declare class OllamaProvider {
    private client;
    private config;
    private retryLogic;
    private shellTool;
    constructor(config: OllamaConfig);
    chat(messages: ChatMessage[], model?: string): Promise<ChatMessage>;
    private formatMessages;
    private getAvailableTools;
    private executeToolCalls;
    getAvailableModels(): Promise<string[]>;
    validateConnection(): Promise<boolean>;
    pullModel(modelName: string): Promise<void>;
    deleteModel(modelName: string): Promise<void>;
    private generateId;
    updateConfig(config: OllamaConfig): void;
    getConfig(): OllamaConfig;
    getModelInfo(modelName: string): Promise<any>;
}
//# sourceMappingURL=OllamaProvider.d.ts.map