{"version": 3, "file": "ThinkingAnimation.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/ThinkingAnimation.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,MAAM,OAAO,iBAAiB;IACpB,MAAM,GAAa;QACzB,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;KACX,CAAC;IAEM,YAAY,GAAG,CAAC,CAAC;IACjB,SAAS,GAAW,CAAC,CAAC;IACtB,UAAU,GAA0B,IAAI,CAAC;IACzC,SAAS,GAAG,KAAK,CAAC;IAClB,UAAU,GAAG,EAAE,CAAC;IAChB,eAAe,GAAG,KAAK,CAAC;IAEhC;QACE,sCAAsC;QACtC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,UAAkB,UAAU;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,kCAAkC;QAClC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEhC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,cAAc;YACd,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACpC,CAAC;QAED,sDAAsD;QACtD,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;YACjC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,gBAAgB;QAChB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED,IAAI;QACF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,oCAAoC;QACpC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,qEAAqE;QACrE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,cAAc;YACd,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAEO,WAAW,CAAC,OAAe;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE7C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CACzB,MAAM,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,cAAc,IAAI,CAAC,EAAE,CAC/D,CAAC;QAEF,wCAAwC;QACxC,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/B,uCAAuC;YACvC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,MAAM,EAAE,CAAC,CAAC;YAC1C,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IACnE,CAAC;IAED,aAAa,CAAC,UAAkB;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,iDAAiD;YACjD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,mCAAmC;IACnC,YAAY;QACV,qDAAqD;QACrD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;IACvD,CAAC;CACF;AAED,MAAM,OAAO,iBAAiB;IACpB,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACnE,YAAY,GAAG,CAAC,CAAC;IACjB,UAAU,GAA0B,IAAI,CAAC;IACzC,SAAS,GAAG,KAAK,CAAC;IAClB,UAAU,GAAG,EAAE,CAAC;IAChB,eAAe,GAAG,KAAK,CAAC;IACxB,cAAc,GAAuB,SAAS,CAAC;IAEvD;QACE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,UAAmB;QACxC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;QAEjC,kCAAkC;QAClC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEhC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACpC,CAAC;QAED,sDAAsD;QACtD,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;YACjC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxC,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,gBAAgB;QAChB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,OAAe,EAAE,UAAmB;QACzC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,mDAAmD;YACnD,IAAI,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;gBAClE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,gBAAgB;oBACtC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;gBACnC,CAAC;YACH,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,IAAI;QACF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,oCAAoC;QACpC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,6DAA6D;QAC7D,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,cAAc;YACd,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IAClC,CAAC;IAEO,WAAW,CAAC,OAAe,EAAE,UAAmB;QACtD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,IAAI,OAAO,EAAE,CAAC,CAAC;QAEjD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,IAAI,IAAI,WAAW,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxD,CAAC;QAED,sDAAsD;QACtD,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,MAAM,EAAE,CAAC,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC;YAC5D,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;IAC1E,CAAC;IAEO,iBAAiB,CAAC,UAAkB;QAC1C,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC;QAE7B,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACzE,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF;AAED,MAAM,OAAO,eAAe;IAC1B,MAAM,CAAC,OAAO,CAAC,OAAe;QAC5B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,OAAe;QAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAAe;QAC5B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,OAAe;QACzB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAAe;QAC5B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;CACF;AAED,yCAAyC;AACzC,MAAM,OAAO,gBAAgB;IACnB,MAAM,CAAC,gBAAgB,GAA8C,EAAE,CAAC;IAEhF,MAAM,CAAC,QAAQ,CAAC,SAAgD;QAC9D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,SAAgD;QAChE,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,MAAM,CAAC,OAAO;QACZ,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACxC,IAAI,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACzB,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,iBAAiB;QACtB,0DAA0D;QAC1D,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACzB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;QACnD,CAAC;IACH,CAAC;;AAGH,mCAAmC;AACnC,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC;IACnC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC/D,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACjE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC;AACpE,CAAC"}