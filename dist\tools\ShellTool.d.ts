import { ShellCommand, ShellResult, ToolDefinition } from '../types/index.js';
export declare class ShellTool {
    private retryLogic;
    private dangerousCommands;
    constructor();
    static getToolDefinition(): ToolDefinition;
    execute(command: ShellCommand): Promise<ShellResult>;
    private translateCommand;
    private prepareCommand;
    private parseCommand;
    private isDangerousCommand;
    private executeCommand;
    validateCommand(command: string): {
        valid: boolean;
        reason?: string;
    };
    executeWithApproval(command: ShellCommand, autoApprove?: boolean): Promise<ShellResult>;
}
//# sourceMappingURL=ShellTool.d.ts.map