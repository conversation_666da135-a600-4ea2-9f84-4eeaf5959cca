import chalk from 'chalk';

export class ThinkingAnimation {
  private frames: string[] = [
    "( ●    )",
    "(  ●   )",
    "(   ●  )",
    "(    ● )",
    "(     ●)",
    "(    ● )",
    "(   ●  )",
    "(  ●   )",
    "( ●    )",
    "(●     )",
  ];

  private currentFrame = 0;
  private startTime: number = 0;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastOutput = '';
  private isTerminalReady = false;

  constructor() {
    // Check if we're in a TTY environment
    this.isTerminalReady = process.stdout.isTTY || false;
  }

  start(message: string = 'Thinking'): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.startTime = Date.now();
    this.currentFrame = 0;
    this.lastOutput = '';

    // Register with animation manager
    AnimationManager.register(this);

    // Only manipulate cursor if we're in a TTY
    if (this.isTerminalReady) {
      // Hide cursor
      process.stdout.write('\x1B[?25l');
    }

    // Use a slightly slower interval to reduce flickering
    this.intervalId = setInterval(() => {
      this.updateFrame(message);
    }, 200);

    // Initial frame
    this.updateFrame(message);
  }

  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    // Unregister from animation manager
    AnimationManager.unregister(this);

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    // Clear the current line and move to next line for subsequent output
    process.stdout.write('\r\x1B[K');

    if (this.isTerminalReady) {
      // Show cursor
      process.stdout.write('\x1B[?25h');
    }

    this.lastOutput = '';
  }

  private updateFrame(message: string): void {
    if (!this.isRunning) {
      return;
    }

    const elapsedSeconds = Math.floor((Date.now() - this.startTime) / 1000);
    const frame = this.frames[this.currentFrame];

    const output = chalk.yellow(
      `🤖 ${message} ${frame} ${chalk.gray(`(${elapsedSeconds}s)`)}`
    );

    // Only update if the output has changed
    if (output !== this.lastOutput) {
      // Clear the line and write new content
      process.stdout.write(`\r\x1B[K${output}`);
      this.lastOutput = output;
    }

    this.currentFrame = (this.currentFrame + 1) % this.frames.length;
  }

  updateMessage(newMessage: string): void {
    if (this.isRunning) {
      // Force an immediate update with the new message
      this.lastOutput = '';
      this.updateFrame(newMessage);
    }
  }

  isActive(): boolean {
    return this.isRunning;
  }

  getElapsedTime(): number {
    return this.isRunning ? Date.now() - this.startTime : 0;
  }

  // Method to handle terminal resize
  handleResize(): void {
    // Re-check TTY status in case terminal state changed
    this.isTerminalReady = process.stdout.isTTY || false;
  }
}

export class ProgressAnimation {
  private progressChars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
  private currentFrame = 0;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastOutput = '';
  private isTerminalReady = false;
  private lastPercentage: number | undefined = undefined;

  constructor() {
    this.isTerminalReady = process.stdout.isTTY || false;
  }

  start(message: string, percentage?: number): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.currentFrame = 0;
    this.lastOutput = '';
    this.lastPercentage = percentage;

    // Register with animation manager
    AnimationManager.register(this);

    // Only manipulate cursor if we're in a TTY
    if (this.isTerminalReady) {
      process.stdout.write('\x1B[?25l');
    }

    // Use a slightly slower interval to reduce flickering
    this.intervalId = setInterval(() => {
      this.updateFrame(message, percentage);
    }, 120);

    // Initial frame
    this.updateFrame(message, percentage);
  }

  update(message: string, percentage?: number): void {
    if (this.isRunning) {
      // Force update if percentage changed significantly
      if (percentage !== undefined && this.lastPercentage !== undefined) {
        if (Math.abs(percentage - this.lastPercentage) >= 1) {
          this.lastOutput = ''; // Force refresh
          this.lastPercentage = percentage;
        }
      }
      this.updateFrame(message, percentage);
    }
  }

  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    // Unregister from animation manager
    AnimationManager.unregister(this);

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    // Clear the line and move to next line for subsequent output
    process.stdout.write('\r\x1B[K');

    if (this.isTerminalReady) {
      // Show cursor
      process.stdout.write('\x1B[?25h');
    }

    this.lastOutput = '';
    this.lastPercentage = undefined;
  }

  private updateFrame(message: string, percentage?: number): void {
    if (!this.isRunning) {
      return;
    }

    const spinner = this.progressChars[this.currentFrame];
    let output = chalk.blue(`${spinner} ${message}`);

    if (percentage !== undefined) {
      const progressBar = this.createProgressBar(percentage);
      output += ` ${progressBar} ${percentage.toFixed(1)}%`;
    }

    // Only update if the output has changed significantly
    if (output !== this.lastOutput) {
      if (this.isTerminalReady) {
        process.stdout.write(`\r\x1B[K${output}`);
      } else {
        process.stdout.write(`\r${output.padEnd(80)}\r${output}`);
      }
      this.lastOutput = output;
    }

    this.currentFrame = (this.currentFrame + 1) % this.progressChars.length;
  }

  private createProgressBar(percentage: number): string {
    const width = 20;
    const filled = Math.floor((percentage / 100) * width);
    const empty = width - filled;

    return chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
  }

  isActive(): boolean {
    return this.isRunning;
  }
}

export class StatusIndicator {
  static success(message: string): void {
    console.log(chalk.green(`✅ ${message}`));
  }

  static error(message: string): void {
    console.log(chalk.red(`❌ ${message}`));
  }

  static warning(message: string): void {
    console.log(chalk.yellow(`⚠️  ${message}`));
  }

  static info(message: string): void {
    console.log(chalk.blue(`ℹ️  ${message}`));
  }

  static loading(message: string): void {
    console.log(chalk.cyan(`⏳ ${message}`));
  }
}

// Global cleanup function for animations
export class AnimationManager {
  private static activeAnimations: (ThinkingAnimation | ProgressAnimation)[] = [];

  static register(animation: ThinkingAnimation | ProgressAnimation): void {
    this.activeAnimations.push(animation);
  }

  static unregister(animation: ThinkingAnimation | ProgressAnimation): void {
    const index = this.activeAnimations.indexOf(animation);
    if (index > -1) {
      this.activeAnimations.splice(index, 1);
    }
  }

  static stopAll(): void {
    this.activeAnimations.forEach(animation => {
      if (animation.isActive()) {
        animation.stop();
      }
    });
    this.activeAnimations = [];
  }

  static handleProcessExit(): void {
    // Ensure cursor is restored and animations are cleaned up
    this.stopAll();
    if (process.stdout.isTTY) {
      process.stdout.write('\x1B[?25h'); // Show cursor
    }
  }
}

// Register global cleanup handlers
if (typeof process !== 'undefined') {
  process.on('exit', () => AnimationManager.handleProcessExit());
  process.on('SIGINT', () => AnimationManager.handleProcessExit());
  process.on('SIGTERM', () => AnimationManager.handleProcessExit());
}
